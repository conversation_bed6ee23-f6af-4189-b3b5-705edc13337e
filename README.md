# SQL UNION Query Standardizer

This solution provides both an immediate fix for your SQL query and an automated tool for future maintenance.

## Files Included

1. **`corrected_union_query.sql`** - Your corrected SQL with all blocks standardized
2. **`sql_union_standardizer.py`** - Automation script for future use
3. **`example_inconsistent_query.sql`** - Example of the problem (before standardization)
4. **`README.md`** - This documentation

## Immediate Solution

The **`corrected_union_query.sql`** file shows your SQL query with all inconsistencies fixed:

### Key Changes Made:
- ✅ **Added missing `CardType` column** to all blocks
- ✅ **Removed inconsistent `WHERE CardType = 'C'` conditions** from all blocks
- ✅ **Standardized all blocks** to match the DEISS_DE reference pattern exactly
- ✅ **Maintained proper company names** and database references

### Before vs After:
**BEFORE (Inconsistent):**
```sql
-- Missing CardType column, has WHERE clause
SELECT 'DEISS_CH' as Company, CardCode, CardName, U_Dim1, U_Dim2, GroupCode, UpdateDate, CreateDate
FROM [SAP_DEISS_CH].[DBO].[OCRD] 
WHERE CardType = 'C'
```

**AFTER (Standardized):**
```sql
-- Includes CardType column, no WHERE clause (matches reference)
SELECT 'DEISS_CH' as Company, CardCode, CardType, CardName, U_Dim1, U_Dim2, GroupCode, UpdateDate, CreateDate
FROM [SAP_DEISS_CH].[DBO].[OCRD] 
```

## Automation Solution

The **`sql_union_standardizer.py`** script provides automated standardization for future maintenance.

### Features:
- 🔍 **Automatic Pattern Detection** - Uses first UNION block as reference
- 🔧 **Column Standardization** - Ensures all blocks have identical column lists
- 📋 **WHERE Clause Standardization** - Applies consistent WHERE conditions
- 🏢 **Company Preservation** - Maintains unique company names and database references
- 📝 **Error Handling** - Provides clear error messages for parsing issues

### Usage:

#### Basic Usage:
```bash
# Standardize and output to console
python sql_union_standardizer.py input.sql

# Standardize and save to file
python sql_union_standardizer.py input.sql output.sql
```

#### Example:
```bash
# Test with the provided example
python sql_union_standardizer.py example_inconsistent_query.sql standardized_output.sql
```

### How It Works:

1. **Parses** the input SQL file and splits it into UNION blocks
2. **Identifies** the reference pattern from the first block
3. **Extracts** column list and WHERE conditions from the reference
4. **Standardizes** all other blocks to match the reference exactly
5. **Preserves** company names and database references
6. **Generates** the corrected SQL output

### Script Architecture:

- **`SQLUnionBlock`** class - Represents individual SELECT blocks
- **`SQLUnionStandardizer`** class - Main processing logic
- **Regex patterns** for robust SQL parsing
- **Error handling** for common issues

## Requirements

- Python 3.6 or higher
- No external dependencies (uses only standard library)

## Benefits

### Immediate:
- ✅ Fixed SQL query ready to use
- ✅ All blocks now have consistent structure
- ✅ Eliminates column mismatch errors

### Long-term:
- 🚀 **Automated maintenance** for similar queries
- 🔄 **Reusable solution** for other UNION standardization tasks
- 📈 **Scalable approach** for larger SQL codebases
- 🛡️ **Error prevention** through consistent patterns

## Testing the Solution

1. **Test the corrected SQL** in your database environment
2. **Verify the automation script** with the provided example:
   ```bash
   python sql_union_standardizer.py example_inconsistent_query.sql test_output.sql
   ```
3. **Compare results** to ensure accuracy

## Future Maintenance

Whenever you encounter similar UNION query inconsistencies:

1. Save the problematic SQL to a file
2. Run the standardizer script
3. Review the output
4. Replace the original with the standardized version

This approach ensures consistent, maintainable SQL code across your entire codebase.
