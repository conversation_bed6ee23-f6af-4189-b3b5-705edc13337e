# SQL UNION查询标准化工具

此解决方案提供了SQL查询的即时修复和未来维护的自动化工具。

## 包含的文件

1. **`原始文件/`** - 存放需要处理的原始SQL文件的文件夹
2. **`修改后文件/`** - 存放处理后的标准化SQL文件的文件夹
3. **`sql_union_standardizer.py`** - 自动化处理脚本
4. **`standardize_sql.bat`** - 一键批量处理的批处理文件
5. **`example_inconsistent_query.sql`** - 问题示例（标准化前）
6. **`corrected_union_query.sql`** - 修正后的SQL示例
7. **`README.md`** - 本文档

## 核心功能

**核心原理：** 无论模板是什么样，第一个UNION ALL之前的代码作为参考模板，第一个UNION ALL之后的所有代码块都会按照这个模板进行标准化。

## 🚀 一键批量处理

**最简单的使用方法：**

1. 将需要处理的SQL文件放入 **`原始文件`** 文件夹
2. 双击运行 **`standardize_sql.bat`**
3. 处理完成的文件会自动保存到 **`修改后文件`** 文件夹，文件名保持不变

### 处理示例：
**处理前（不一致）：**
```sql
-- 第一个块（参考模板）
SELECT 'DEISS_DE' as Company, CardCode, CardType, CardName, U_Dim1, U_Dim2, GroupCode, UpdateDate, CreateDate
FROM [SAP_DEISS].[DBO].[OCRD]

UNION ALL

-- 第二个块（缺少CardType列，有WHERE条件）
SELECT 'DEISS_CH' as Company, CardCode, CardName, U_Dim1, U_Dim2, GroupCode, UpdateDate, CreateDate
FROM [SAP_DEISS_CH].[DBO].[OCRD]
WHERE CardType = 'C'
```

**处理后（标准化）：**
```sql
-- 所有块都按照第一个块的模板标准化
SELECT 'DEISS_DE' as Company, CardCode, CardType, CardName, U_Dim1, U_Dim2, GroupCode, UpdateDate, CreateDate
FROM [SAP_DEISS].[DBO].[OCRD]

UNION ALL

SELECT 'DEISS_CH' as Company, CardCode, CardType, CardName, U_Dim1, U_Dim2, GroupCode, UpdateDate, CreateDate
FROM [SAP_DEISS_CH].[DBO].[OCRD]
```

## 高级用法

### 命令行使用方法：

#### 单文件处理：
```bash
# 标准化并输出到控制台
python sql_union_standardizer.py input.sql

# 标准化并保存到文件
python sql_union_standardizer.py input.sql output.sql
```

#### 批量处理：
```bash
# 批量处理整个文件夹
python sql_union_standardizer.py --batch 原始文件 修改后文件
```

### 功能特点：
- 🔍 **自动模板检测** - 使用第一个UNION块作为参考模板
- 🔧 **列标准化** - 确保所有块具有相同的列列表
- 📋 **WHERE子句标准化** - 应用一致的WHERE条件
- 🏢 **公司名称保留** - 保持唯一的公司名称和数据库引用
- 📁 **批量处理** - 一次处理整个文件夹中的所有SQL文件
- 📝 **错误处理** - 提供清晰的错误消息

## 工作原理

1. **解析** 输入的SQL文件并将其分割为UNION块
2. **识别** 第一个块作为参考模板
3. **提取** 参考模板的列列表和WHERE条件
4. **标准化** 所有其他块以完全匹配参考模板
5. **保留** 公司名称和数据库引用
6. **生成** 修正后的SQL输出

## 系统要求

- Python 3.6 或更高版本
- 无需外部依赖（仅使用标准库）

## 使用步骤

### 方法一：一键批量处理（推荐）
1. 将需要处理的SQL文件放入 **`原始文件`** 文件夹
2. 双击运行 **`standardize_sql.bat`**
3. 查看 **`修改后文件`** 文件夹中的结果

### 方法二：命令行处理
```bash
# 批量处理
python sql_union_standardizer.py --batch 原始文件 修改后文件

# 单文件处理
python sql_union_standardizer.py input.sql output.sql
```

## 优势

### 即时效果：
- ✅ 修复SQL查询，立即可用
- ✅ 所有块现在具有一致的结构
- ✅ 消除列不匹配错误

### 长期效益：
- 🚀 **自动化维护** 类似查询
- 🔄 **可重用解决方案** 用于其他UNION标准化任务
- 📈 **可扩展方法** 适用于更大的SQL代码库
- 🛡️ **通过一致模式防止错误**

## 测试解决方案

1. 将测试SQL文件放入 `原始文件` 文件夹
2. 运行批处理脚本
3. 检查 `修改后文件` 文件夹中的结果
4. 在数据库环境中测试修正后的SQL

## 未来维护

每当遇到类似的UNION查询不一致问题时：

1. 将有问题的SQL文件保存到 `原始文件` 文件夹
2. 运行标准化脚本
3. 检查输出结果
4. 使用标准化版本替换原始版本

这种方法确保整个代码库中SQL代码的一致性和可维护性。
