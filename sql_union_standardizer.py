#!/usr/bin/env python3
"""
SQL UNION Query Standardizer

This script automatically standardizes SQL UNION queries by:
1. Using the first UNION block as the reference pattern
2. Standardizing all other blocks to match the reference exactly
3. Preserving company names and database references while ensuring consistent structure

Usage:
    python sql_union_standardizer.py input.sql output.sql
    python sql_union_standardizer.py input.sql  # outputs to stdout
"""

import re
import sys
import argparse
from typing import List, Dict, Tuple, Optional


class SQLUnionBlock:
    """Represents a single SELECT block in a UNION query"""
    
    def __init__(self, company: str, columns: List[str], database: str, table: str, where_clause: str = ""):
        self.company = company
        self.columns = columns
        self.database = database
        self.table = table
        self.where_clause = where_clause.strip()
    
    def __str__(self):
        columns_str = ", ".join(self.columns)
        query = f"SELECT {columns_str}\nFROM {self.database}.{self.table}"
        if self.where_clause:
            query += f"\n{self.where_clause}"
        return query


class SQLUnionStandardizer:
    """Main class for standardizing SQL UNION queries"""
    
    def __init__(self):
        # Regex patterns for parsing SQL blocks
        self.union_split_pattern = re.compile(r'\bUNION\s+ALL\b', re.IGNORECASE)
        self.select_pattern = re.compile(
            r"SELECT\s+(.*?)\s+FROM\s+(\[?[^\[\]\s]+\]?(?:\.\[?[^\[\]\s]+\]?)*)\s*(?:WHERE\s+(.*))?$",
            re.IGNORECASE | re.DOTALL
        )
        self.company_pattern = re.compile(r"'([^']+)'\s+as\s+Company", re.IGNORECASE)
    
    def parse_sql_file(self, file_path: str) -> str:
        """Read SQL file content"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
        except FileNotFoundError:
            raise FileNotFoundError(f"SQL file not found: {file_path}")
        except Exception as e:
            raise Exception(f"Error reading SQL file: {e}")
    
    def extract_union_blocks(self, sql_content: str) -> List[str]:
        """Split SQL content into individual UNION blocks"""
        # Remove comments and normalize whitespace
        sql_content = re.sub(r'--.*$', '', sql_content, flags=re.MULTILINE)
        sql_content = re.sub(r'\s+', ' ', sql_content).strip()
        
        # Split by UNION ALL
        blocks = self.union_split_pattern.split(sql_content)
        return [block.strip() for block in blocks if block.strip()]
    
    def parse_block(self, block_sql: str) -> SQLUnionBlock:
        """Parse a single SELECT block into a SQLUnionBlock object"""
        match = self.select_pattern.match(block_sql.strip())
        if not match:
            raise ValueError(f"Could not parse SQL block: {block_sql[:100]}...")
        
        select_part, from_part, where_part = match.groups()
        
        # Extract company name
        company_match = self.company_pattern.search(select_part)
        if not company_match:
            raise ValueError(f"Could not find company name in: {select_part}")
        
        company = company_match.group(1)
        
        # Parse columns (remove company column for processing)
        columns_raw = select_part.strip()
        # Remove the company part and clean up
        columns_raw = re.sub(r"'[^']+'\s+as\s+Company,?\s*", '', columns_raw, flags=re.IGNORECASE)
        columns = [col.strip() for col in columns_raw.split(',') if col.strip()]
        
        # Add company column back at the beginning
        columns.insert(0, f"'{company}' as Company")
        
        # Parse database and table
        database_table = from_part.strip()
        
        # Handle WHERE clause
        where_clause = where_part.strip() if where_part else ""
        if where_clause:
            where_clause = f"WHERE {where_clause}"
        
        return SQLUnionBlock(company, columns, database_table, "", where_clause)
    
    def standardize_blocks(self, blocks: List[SQLUnionBlock]) -> List[SQLUnionBlock]:
        """Standardize all blocks to match the first (reference) block"""
        if not blocks:
            raise ValueError("No blocks to standardize")
        
        reference_block = blocks[0]
        reference_columns = reference_block.columns[1:]  # Exclude company column
        reference_where = reference_block.where_clause
        
        standardized_blocks = []
        
        for block in blocks:
            # Create standardized columns list with the block's company name
            standardized_columns = [f"'{block.company}' as Company"] + reference_columns
            
            # Create standardized block
            standardized_block = SQLUnionBlock(
                company=block.company,
                columns=standardized_columns,
                database=block.database,
                table=block.table,
                where_clause=reference_where
            )
            standardized_blocks.append(standardized_block)
        
        return standardized_blocks
    
    def generate_standardized_sql(self, blocks: List[SQLUnionBlock]) -> str:
        """Generate the final standardized SQL query"""
        sql_parts = []
        
        for i, block in enumerate(blocks):
            if i > 0:
                sql_parts.append("\nUNION ALL\n")
            
            columns_str = ", ".join(block.columns)
            query_part = f"SELECT {columns_str}\nFROM {block.database}"
            
            if block.where_clause:
                query_part += f"\n{block.where_clause}"
            
            sql_parts.append(query_part)
        
        return "".join(sql_parts)
    
    def standardize_sql_file(self, input_file: str, output_file: Optional[str] = None) -> str:
        """Main method to standardize a SQL file"""
        try:
            # Read and parse the SQL file
            sql_content = self.parse_sql_file(input_file)
            
            # Extract UNION blocks
            block_strings = self.extract_union_blocks(sql_content)
            
            if len(block_strings) < 2:
                raise ValueError("SQL file must contain at least 2 UNION blocks")
            
            # Parse each block
            blocks = [self.parse_block(block) for block in block_strings]
            
            # Standardize blocks
            standardized_blocks = self.standardize_blocks(blocks)
            
            # Generate final SQL
            standardized_sql = self.generate_standardized_sql(standardized_blocks)
            
            # Add header comment
            header = "-- Standardized SQL UNION Query\n"
            header += "-- All blocks standardized to match the reference pattern\n\n"
            standardized_sql = header + standardized_sql
            
            # Output result
            if output_file:
                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write(standardized_sql)
                print(f"Standardized SQL written to: {output_file}")
            else:
                print(standardized_sql)
            
            return standardized_sql
            
        except Exception as e:
            print(f"Error: {e}", file=sys.stderr)
            sys.exit(1)


def main():
    parser = argparse.ArgumentParser(
        description="Standardize SQL UNION queries to match a reference pattern"
    )
    parser.add_argument("input_file", help="Input SQL file path")
    parser.add_argument("output_file", nargs="?", help="Output SQL file path (optional)")
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose output")
    
    args = parser.parse_args()
    
    standardizer = SQLUnionStandardizer()
    standardizer.standardize_sql_file(args.input_file, args.output_file)


if __name__ == "__main__":
    main()
