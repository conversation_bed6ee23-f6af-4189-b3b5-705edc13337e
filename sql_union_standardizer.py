#!/usr/bin/env python3
"""
SQL UNION查询标准化工具

此脚本自动标准化SQL UNION查询：
1. 使用第一个UNION块作为参考模板
2. 将所有其他块标准化以完全匹配参考模板
3. 保留公司名称和数据库引用，同时确保结构一致

用法:
    python sql_union_standardizer.py input.sql output.sql
    python sql_union_standardizer.py input.sql  # 输出到控制台
    python sql_union_standardizer.py --batch 原始文件 修改后文件  # 批量处理
"""

import re
import sys
import os
import argparse
from typing import List, Dict, Tuple, Optional
from pathlib import Path

# 设置控制台编码以支持中文输出
if sys.platform == "win32":
    import codecs
    sys.stdout = codecs.getwriter("utf-8")(sys.stdout.detach())
    sys.stderr = codecs.getwriter("utf-8")(sys.stderr.detach())


class SQLUnionBlock:
    """表示UNION查询中的单个SELECT块"""

    def __init__(self, company: str, columns: List[str], database: str, table: str, where_clause: str = ""):
        self.company = company
        self.columns = columns
        self.database = database
        self.table = table
        self.where_clause = where_clause.strip()

    def __str__(self):
        columns_str = ", ".join(self.columns)
        query = f"SELECT {columns_str}\nFROM {self.database}.{self.table}"
        if self.where_clause:
            query += f"\n{self.where_clause}"
        return query


class SQLUnionStandardizer:
    """标准化SQL UNION查询的主类"""
    
    def __init__(self):
        # Regex patterns for parsing SQL blocks
        self.union_split_pattern = re.compile(r'\bUNION\s+ALL\b', re.IGNORECASE)
        self.select_pattern = re.compile(
            r"SELECT\s+(.*?)\s+FROM\s+(\[?[^\[\]\s]+\]?(?:\.\[?[^\[\]\s]+\]?)*)\s*(?:WHERE\s+(.*))?$",
            re.IGNORECASE | re.DOTALL
        )
        self.company_pattern = re.compile(r"'([^']+)'\s+as\s+Company", re.IGNORECASE)
    
    def parse_sql_file(self, file_path: str) -> str:
        """读取SQL文件内容"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
        except FileNotFoundError:
            raise FileNotFoundError(f"找不到SQL文件: {file_path}")
        except Exception as e:
            raise Exception(f"读取SQL文件时出错: {e}")

    def extract_union_blocks(self, sql_content: str) -> List[str]:
        """将SQL内容分割为单独的UNION块"""
        # 移除注释并规范化空白字符
        sql_content = re.sub(r'--.*$', '', sql_content, flags=re.MULTILINE)
        sql_content = re.sub(r'\s+', ' ', sql_content).strip()

        # 按UNION ALL分割
        blocks = self.union_split_pattern.split(sql_content)
        return [block.strip() for block in blocks if block.strip()]
    
    def parse_block(self, block_sql: str) -> SQLUnionBlock:
        """Parse a single SELECT block into a SQLUnionBlock object"""
        match = self.select_pattern.match(block_sql.strip())
        if not match:
            raise ValueError(f"Could not parse SQL block: {block_sql[:100]}...")
        
        select_part, from_part, where_part = match.groups()
        
        # Extract company name
        company_match = self.company_pattern.search(select_part)
        if not company_match:
            raise ValueError(f"Could not find company name in: {select_part}")
        
        company = company_match.group(1)
        
        # Parse columns (remove company column for processing)
        columns_raw = select_part.strip()
        # Remove the company part and clean up
        columns_raw = re.sub(r"'[^']+'\s+as\s+Company,?\s*", '', columns_raw, flags=re.IGNORECASE)
        columns = [col.strip() for col in columns_raw.split(',') if col.strip()]
        
        # Add company column back at the beginning
        columns.insert(0, f"'{company}' as Company")
        
        # Parse database and table
        database_table = from_part.strip()
        
        # Handle WHERE clause
        where_clause = where_part.strip() if where_part else ""
        if where_clause:
            where_clause = f"WHERE {where_clause}"
        
        return SQLUnionBlock(company, columns, database_table, "", where_clause)
    
    def standardize_blocks(self, blocks: List[SQLUnionBlock]) -> List[SQLUnionBlock]:
        """Standardize all blocks to match the first (reference) block"""
        if not blocks:
            raise ValueError("No blocks to standardize")
        
        reference_block = blocks[0]
        reference_columns = reference_block.columns[1:]  # Exclude company column
        reference_where = reference_block.where_clause
        
        standardized_blocks = []
        
        for block in blocks:
            # Create standardized columns list with the block's company name
            standardized_columns = [f"'{block.company}' as Company"] + reference_columns
            
            # Create standardized block
            standardized_block = SQLUnionBlock(
                company=block.company,
                columns=standardized_columns,
                database=block.database,
                table=block.table,
                where_clause=reference_where
            )
            standardized_blocks.append(standardized_block)
        
        return standardized_blocks
    
    def generate_standardized_sql(self, blocks: List[SQLUnionBlock]) -> str:
        """Generate the final standardized SQL query"""
        sql_parts = []
        
        for i, block in enumerate(blocks):
            if i > 0:
                sql_parts.append("\nUNION ALL\n")
            
            columns_str = ", ".join(block.columns)
            query_part = f"SELECT {columns_str}\nFROM {block.database}"
            
            if block.where_clause:
                query_part += f"\n{block.where_clause}"
            
            sql_parts.append(query_part)
        
        return "".join(sql_parts)
    
    def standardize_sql_file(self, input_file: str, output_file: Optional[str] = None) -> str:
        """Main method to standardize a SQL file"""
        try:
            # Read and parse the SQL file
            sql_content = self.parse_sql_file(input_file)
            
            # Extract UNION blocks
            block_strings = self.extract_union_blocks(sql_content)
            
            if len(block_strings) < 2:
                raise ValueError("SQL file must contain at least 2 UNION blocks")
            
            # Parse each block
            blocks = [self.parse_block(block) for block in block_strings]
            
            # Standardize blocks
            standardized_blocks = self.standardize_blocks(blocks)
            
            # Generate final SQL
            standardized_sql = self.generate_standardized_sql(standardized_blocks)
            
            # Add header comment
            header = "-- Standardized SQL UNION Query\n"
            header += "-- All blocks standardized to match the reference pattern\n\n"
            standardized_sql = header + standardized_sql
            
            # Output result
            if output_file:
                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write(standardized_sql)
                print(f"Standardized SQL written to: {output_file}")
            else:
                print(standardized_sql)
            
            return standardized_sql

        except Exception as e:
            print(f"错误: {e}", file=sys.stderr)
            sys.exit(1)

    def batch_process_directory(self, input_dir: str, output_dir: str) -> None:
        """批量处理目录中的所有SQL文件"""
        input_path = Path(input_dir)
        output_path = Path(output_dir)

        if not input_path.exists():
            raise FileNotFoundError(f"输入目录不存在: {input_dir}")

        if not input_path.is_dir():
            raise ValueError(f"输入路径不是目录: {input_dir}")

        # 创建输出目录（如果不存在）
        output_path.mkdir(parents=True, exist_ok=True)

        # 查找所有SQL文件
        sql_files = list(input_path.glob("*.sql"))

        if not sql_files:
            print(f"在目录 {input_dir} 中没有找到SQL文件")
            return

        print(f"找到 {len(sql_files)} 个SQL文件，开始批量处理...")

        success_count = 0
        error_count = 0

        for sql_file in sql_files:
            try:
                print(f"正在处理: {sql_file.name}")

                # 读取和处理SQL文件
                sql_content = self.parse_sql_file(str(sql_file))
                block_strings = self.extract_union_blocks(sql_content)

                if len(block_strings) < 2:
                    print(f"  跳过 {sql_file.name}: 文件必须包含至少2个UNION块")
                    continue

                # 解析和标准化
                blocks = [self.parse_block(block) for block in block_strings]
                standardized_blocks = self.standardize_blocks(blocks)
                standardized_sql = self.generate_standardized_sql(standardized_blocks)

                # 添加头部注释
                header = f"-- 标准化的SQL UNION查询\n"
                header += f"-- 原文件: {sql_file.name}\n"
                header += f"-- 所有块已标准化以匹配参考模式\n\n"
                standardized_sql = header + standardized_sql

                # 保存到输出目录
                output_file = output_path / sql_file.name
                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write(standardized_sql)

                print(f"  ✓ 成功处理并保存到: {output_file}")
                success_count += 1

            except Exception as e:
                print(f"  ✗ 处理 {sql_file.name} 时出错: {e}")
                error_count += 1

        print(f"\n批量处理完成:")
        print(f"  成功处理: {success_count} 个文件")
        print(f"  处理失败: {error_count} 个文件")


def main():
    parser = argparse.ArgumentParser(
        description="标准化SQL UNION查询以匹配参考模式"
    )
    parser.add_argument("input", help="输入SQL文件路径或输入目录路径（批量模式）")
    parser.add_argument("output", nargs="?", help="输出SQL文件路径或输出目录路径（可选）")
    parser.add_argument("--batch", "-b", action="store_true", help="批量处理模式：处理整个目录")
    parser.add_argument("--verbose", "-v", action="store_true", help="详细输出")

    args = parser.parse_args()

    standardizer = SQLUnionStandardizer()

    if args.batch:
        if not args.output:
            print("错误: 批量模式需要指定输出目录", file=sys.stderr)
            sys.exit(1)
        standardizer.batch_process_directory(args.input, args.output)
    else:
        standardizer.standardize_sql_file(args.input, args.output)


if __name__ == "__main__":
    main()
