SELECT 'DEISS_DE' as Company, CardCode, CardType, CardName, U_Dim1, U_Dim2, GroupCode, UpdateDate, CreateDate, U_Hafen
FROM [SVSAPDB].[SAP_DEISS].[DBO].[OCRD] 

UNION ALL

SELECT 'DEISS_CH' as Company, CardCode, CardType, CardName, U_Dim1, U_Dim2, GroupCode, UpdateDate, CreateDate, U_Hafen
FROM [SVSAPDB].[SAP_SWISS].[DBO].[OCRD] 

UNION ALL

SELECT 'DEISS_NL' as Company, CardCode, CardName, U_Dim1, U_Dim2, GroupCode, UpdateDate, CreateDate
FROM [SVSAPDB].[SAP_NEDERLAND].[DBO].[OCRD] 
WHERE CardType = 'C'

UNION ALL

SELECT 'BINGOLD_DE' as Company, CardCode, CardName, U_Dim1, U_Dim2, GroupCode, UpdateDate, CreateDate
FROM [SVSAPDB].[SAP_BINGOLD].[DBO].[OCRD] 
WHERE CardType = 'C'

UNION ALL

SELECT 'SUND_COLOR' as Company, CardCode, CardName, U_Dim1, U_Dim2, GroupCode, UpdateDate, CreateDate
FROM [SVSAPDB].[SAP_BERASIT].[DBO].[OCRD] 
WHERE CardType = 'C'

UNION ALL

SELECT 'FIPP_DE' as Company, CardCode, CardName, U_Dim1, U_Dim2, GroupCode, UpdateDate, CreateDate
FROM [SVSAPDB].[SAP_FIPP].[DBO].[OCRD] 
WHERE CardType = 'C'

UNION ALL

SELECT 'DEISS_AT' as Company, CardCode, CardName, U_Dim1, U_Dim2, GroupCode, UpdateDate, CreateDate
FROM [SVSAPDB].[SAP_AUSTRIA].[DBO].[OCRD] 
WHERE CardType = 'C'
