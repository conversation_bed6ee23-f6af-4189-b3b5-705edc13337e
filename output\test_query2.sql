-- 标准化的SQL UNION查询
-- 原文件: test_query2.sql
-- 所有块已标准化以匹配参考模式

SELECT 'MAIN_DB' as Company, ID, Name, Status, Region, CreatedDate
FROM [MAIN_DATABASE].[DBO].[CUSTOMERS]
WHERE Status = 'Active'
UNION ALL
SELECT 'BACKUP_DB' as Company, ID, Name, Status, Region, CreatedDate
FROM [BACKUP_DATABASE].[DBO].[CUSTOMERS]
WHERE Status = 'Active'
UNION ALL
SELECT 'ARCHIVE_DB' as Company, ID, Name, Status, Region, CreatedDate
FROM [ARCHIVE_DATABASE].[DBO].[CUSTOMERS]
WHERE Status = 'Active'
UNION ALL
SELECT 'TEST_DB' as Company, ID, Name, Status, Region, CreatedDate
FROM [TEST_DATABASE].[DBO].[CUSTOMERS]
WHERE Status = 'Active'