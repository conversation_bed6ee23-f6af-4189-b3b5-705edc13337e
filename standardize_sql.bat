@echo off
REM SQL UNION Query Standardizer - Windows Batch Script
REM Usage: standardize_sql.bat input.sql [output.sql]

if "%1"=="" (
    echo Usage: standardize_sql.bat input.sql [output.sql]
    echo.
    echo Examples:
    echo   standardize_sql.bat my_query.sql
    echo   standardize_sql.bat my_query.sql standardized_query.sql
    exit /b 1
)

if "%2"=="" (
    echo Standardizing %1 and outputting to console...
    python sql_union_standardizer.py "%1"
) else (
    echo Standardizing %1 and saving to %2...
    python sql_union_standardizer.py "%1" "%2"
    echo Done!
)
