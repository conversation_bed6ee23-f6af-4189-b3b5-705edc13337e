-- Example of inconsistent SQL UNION query (BEFORE standardization)
-- This demonstrates the problem you described

SELECT 'DEISS_DE' as Company, CardCode, CardType, CardName, U_Dim1, U_Dim2, GroupCode, UpdateDate, CreateDate
FROM [SAP_DEISS].[DBO].[OCRD] 

UNION ALL

SELECT 'DEISS_CH' as Company, CardCode, CardName, U_Dim1, U_Dim2, GroupCode, UpdateDate, CreateDate
FROM [SAP_DEISS_CH].[DBO].[OCRD] 
WHERE CardType = 'C'

UNION ALL

SELECT 'DEISS_NL' as Company, CardCode, CardName, U_Dim1, U_Dim2, GroupCode, UpdateDate, CreateDate
FROM [SAP_DEISS_NL].[DBO].[OCRD] 
WHERE CardType = 'C'

UNION ALL

SELECT 'BINGOLD_DE' as Company, CardCode, CardName, U_Dim1, U_Dim2, GroupCode, UpdateDate, CreateDate
FROM [SAP_BINGOLD].[DBO].[OCRD] 
WHERE CardType = 'C'

UNION ALL

SELECT 'SUND_COLOR' as Company, CardCode, CardName, U_Dim1, U_Dim2, GroupCode, UpdateDate, CreateDate
FROM [SAP_SUND_COLOR].[DBO].[OCRD] 
WHERE CardType = 'C'

UNION ALL

SELECT 'FIPP_DE' as Company, CardCode, CardName, U_Dim1, U_Dim2, GroupCode, UpdateDate, CreateDate
FROM [SAP_FIPP].[DBO].[OCRD] 
WHERE CardType = 'C'

UNION ALL

SELECT 'DEISS_AT' as Company, CardCode, CardName, U_Dim1, U_Dim2, GroupCode, UpdateDate, CreateDate
FROM [SAP_DEISS_AT].[DBO].[OCRD]
WHERE CardType = 'C'
