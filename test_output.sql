-- Standardized SQL UNION Query
-- All blocks standardized to match the reference pattern

SELECT 'DEISS_DE' as Company, CardCode, CardType, CardName, U_Dim1, U_Dim2, GroupCode, UpdateDate, CreateDate
FROM [SAP_DEISS].[DBO].[OCRD]
UNION ALL
SELECT 'DEISS_CH' as Company, CardCode, CardType, CardName, U_Dim1, U_Dim2, GroupCode, UpdateDate, CreateDate
FROM [SAP_DEISS_CH].[DBO].[OCRD]
UNION ALL
SELECT 'DEISS_NL' as Company, CardCode, CardType, CardName, U_Dim1, U_Dim2, GroupCode, UpdateDate, CreateDate
FROM [SAP_DEISS_NL].[DBO].[OCRD]
UNION ALL
SELECT 'BINGOLD_DE' as Company, CardCode, CardType, CardName, U_Dim1, U_Dim2, GroupCode, UpdateDate, CreateDate
FROM [SAP_BINGOLD].[DBO].[OCRD]
UNION ALL
SELECT 'SUND_COLOR' as Company, CardCode, CardType, CardName, U_Dim1, U_Dim2, GroupCode, UpdateDate, CreateDate
FROM [SAP_SUND_COLOR].[DBO].[OCRD]
UNION ALL
SELECT 'FIPP_DE' as Company, CardCode, CardType, CardName, U_Dim1, U_Dim2, GroupCode, UpdateDate, CreateDate
FROM [SAP_FIPP].[DBO].[OCRD]
UNION ALL
SELECT 'DEISS_AT' as Company, CardCode, CardType, CardName, U_Dim1, U_Dim2, GroupCode, UpdateDate, CreateDate
FROM [SAP_DEISS_AT].[DBO].[OCRD]