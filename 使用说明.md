# SQL UNION查询标准化工具 - 使用说明

## 🎯 核心功能

**核心原理：** 无论模板是什么样，第一个UNION ALL之前的代码作为参考模板，第一个UNION ALL之后的所有代码块都会按照这个模板进行标准化。

## 📁 文件夹结构

```
SQL_Anpassung/
├── input/                    # 存放原始SQL文件
├── output/                   # 存放处理后的SQL文件
├── sql_union_standardizer.py # 核心处理脚本
├── process_all.bat           # 一键批量处理
└── 使用说明.md               # 本文档
```

## 🚀 一键使用方法

### 步骤1：放入文件
将需要处理的SQL文件放入 **`input`** 文件夹

### 步骤2：运行处理
双击运行 **`process_all.bat`**

### 步骤3：获取结果
处理完成的文件会自动保存到 **`output`** 文件夹，文件名保持不变

## 📋 处理示例

### 处理前（不一致）：
```sql
-- 第一个块（参考模板）
SELECT 'DEISS_DE' as Company, CardCode, CardType, CardName, U_Dim1, U_Dim2, GroupCode, UpdateDate, CreateDate
FROM [SAP_DEISS].[DBO].[OCRD] 

UNION ALL

-- 第二个块（缺少CardType列，有WHERE条件）
SELECT 'DEISS_CH' as Company, CardCode, CardName, U_Dim1, U_Dim2, GroupCode, UpdateDate, CreateDate
FROM [SAP_DEISS_CH].[DBO].[OCRD] 
WHERE CardType = 'C'
```

### 处理后（标准化）：
```sql
-- 所有块都按照第一个块的模板标准化
SELECT 'DEISS_DE' as Company, CardCode, CardType, CardName, U_Dim1, U_Dim2, GroupCode, UpdateDate, CreateDate
FROM [SAP_DEISS].[DBO].[OCRD] 

UNION ALL

SELECT 'DEISS_CH' as Company, CardCode, CardType, CardName, U_Dim1, U_Dim2, GroupCode, UpdateDate, CreateDate
FROM [SAP_DEISS_CH].[DBO].[OCRD] 
```

## 🔧 高级用法

### 命令行使用：

#### 批量处理：
```bash
python sql_union_standardizer.py --batch input output
```

#### 单文件处理：
```bash
python sql_union_standardizer.py input.sql output.sql
```

## ✅ 功能特点

- 🔍 **自动模板检测** - 使用第一个UNION块作为参考模板
- 🔧 **列标准化** - 确保所有块具有相同的列列表
- 📋 **WHERE子句标准化** - 应用一致的WHERE条件
- 🏢 **公司名称保留** - 保持唯一的公司名称和数据库引用
- 📁 **批量处理** - 一次处理整个文件夹中的所有SQL文件
- 📝 **错误处理** - 提供清晰的错误消息
- 📄 **文件名保持** - 输出文件名与输入文件名相同

## 🛠️ 系统要求

- Python 3.6 或更高版本
- Windows操作系统
- 无需外部依赖（仅使用标准库）

## 🎯 适用场景

- SAP数据库UNION查询标准化
- 多数据源查询一致性维护
- 大批量SQL文件处理
- 代码库维护和重构

## 📞 故障排除

### 问题1：找不到Python
**解决方案：** 确保已安装Python并添加到系统PATH

### 问题2：文件编码问题
**解决方案：** 确保SQL文件使用UTF-8编码保存

### 问题3：处理失败
**解决方案：** 检查SQL文件是否包含至少2个UNION ALL块

## 💡 使用技巧

1. **备份原文件** - 处理前建议备份原始文件
2. **检查结果** - 处理后检查output文件夹中的结果
3. **测试查询** - 在数据库中测试标准化后的查询
4. **批量处理** - 一次性处理多个文件提高效率

---

**注意：** 此工具专门设计用于标准化SQL UNION查询，确保所有UNION块与第一个块的结构完全一致。
