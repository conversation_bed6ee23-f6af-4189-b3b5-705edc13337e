-- 标准化的SQL UNION查询
-- 原文件: 测试查询1.sql
-- 所有块已标准化以匹配参考模式

SELECT 'DEISS_DE' as Company, CardCode, CardType, CardName, U_Dim1, U_Dim2, GroupCode, UpdateDate, CreateDate
FROM [SAP_DEISS].[DBO].[OCRD]
UNION ALL
SELECT 'DEISS_CH' as Company, CardCode, CardType, CardName, U_Dim1, U_Dim2, GroupCode, UpdateDate, CreateDate
FROM [SAP_DEISS_CH].[DBO].[OCRD]
UNION ALL
SELECT 'DEISS_NL' as Company, CardCode, CardType, CardName, U_Dim1, U_Dim2, GroupCode, UpdateDate, CreateDate
FROM [SAP_DEISS_NL].[DBO].[OCRD]
UNION ALL
SELECT 'BINGOLD_DE' as Company, CardCode, CardType, CardName, U_Dim1, U_Dim2, GroupCode, UpdateDate, CreateDate
FROM [SAP_BINGOLD].[DBO].[OCRD]