-- 测试查询2：另一个不一致的UNION查询示例
-- 第一个块有WHERE条件，其他块缺少WHERE条件和列

SELECT 'MAIN_DB' as Company, ID, Name, Status, Region, CreatedDate
FROM [MAIN_DATABASE].[DBO].[CUSTOMERS] 
WHERE Status = 'Active'

UNION ALL

SELECT 'BACKUP_DB' as Company, ID, Name, Region, CreatedDate
FROM [BACKUP_DATABASE].[DBO].[CUSTOMERS] 

UNION ALL

SELECT 'ARCHIVE_DB' as Company, ID, Name, Region, CreatedDate
FROM [ARCHIVE_DATABASE].[DBO].[CUSTOMERS] 

UNION ALL

SELECT 'TEST_DB' as Company, ID, Name, Region, CreatedDate
FROM [TEST_DATABASE].[DBO].[CUSTOMERS]
