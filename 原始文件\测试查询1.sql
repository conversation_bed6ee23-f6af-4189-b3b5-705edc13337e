-- 测试查询1：不一致的UNION查询示例
-- 第一个块作为参考模板，其他块缺少列和有不同的WHERE条件

SELECT 'DEISS_DE' as Company, CardCode, CardType, CardName, U_Dim1, U_Dim2, GroupCode, UpdateDate, CreateDate
FROM [SAP_DEISS].[DBO].[OCRD] 

UNION ALL

SELECT 'DEISS_CH' as Company, CardCode, CardName, U_Dim1, U_Dim2, GroupCode, UpdateDate, CreateDate
FROM [SAP_DEISS_CH].[DBO].[OCRD] 
WHERE CardType = 'C'

UNION ALL

SELECT 'DEISS_NL' as Company, CardCode, CardName, U_Dim1, U_Dim2, GroupCode, UpdateDate, CreateDate
FROM [SAP_DEISS_NL].[DBO].[OCRD] 
WHERE CardType = 'C'

UNION ALL

SELECT 'BINGOLD_DE' as Company, CardCode, CardName, U_Dim1, U_Dim2, GroupCode, UpdateDate, CreateDate
FROM [SAP_BINGOLD].[DBO].[OCRD] 
WHERE CardType = 'C'
